/* See the Tailwind configuration guide for advanced usage
   https://tailwindcss.com/docs/configuration */

@import "tailwindcss" source(none);
@source "../css";
@source "../js";
@source "../../lib/tunez_web";
@source "../../deps/ash_authentication_phoenix";

@plugin "@tailwindcss/forms";

/* A Tailwind plugin that makes "hero-#{ICON}" classes available.
      The heroicons installation itself is managed by your mix.exs */
@plugin "../vendor/heroicons";

@import "./theme.css" layer(theme);

/* Add variants based on LiveView classes */
@custom-variant phx-click-loading (.phx-click-loading&, .phx-click-loading &);
@custom-variant phx-submit-loading (.phx-submit-loading&, .phx-submit-loading &);
@custom-variant phx-change-loading (.phx-change-loading&, .phx-change-loading &);

@custom-variant error (.error&, .error &);
@custom-variant dark (&:where(.dark, .dark *));

/* Make LiveView wrapper divs transparent for layout */
[data-phx-root-id] {
  display: contents;
}

/* This file is for your main application CSS */
