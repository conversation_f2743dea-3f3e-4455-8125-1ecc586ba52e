@theme {
  --color-primary-50: var(--color-indigo-50);
  --color-primary-100: var(--color-indigo-100);
  --color-primary-200: var(--color-indigo-200);
  --color-primary-300: var(--color-indigo-300);
  --color-primary-400: var(--color-indigo-400);
  --color-primary-500: var(--color-indigo-500);
  --color-primary-600: var(--color-indigo-600);
  --color-primary-700: var(--color-indigo-700);
  --color-primary-800: var(--color-indigo-800);
  --color-primary-900: var(--color-indigo-900);
  --color-primary-950: var(--color-indigo-950);

  --color-accent-50: var(--color-teal-50);
  --color-accent-100: var(--color-teal-100);
  --color-accent-200: var(--color-teal-200);
  --color-accent-300: var(--color-teal-300);
  --color-accent-400: var(--color-teal-400);
  --color-accent-500: var(--color-teal-500);
  --color-accent-600: var(--color-teal-600);
  --color-accent-700: var(--color-teal-700);
  --color-accent-800: var(--color-teal-800);
  --color-accent-900: var(--color-teal-900);
  --color-accent-950: var(--color-teal-950);

  --color-error-50: var(--color-red-50);
  --color-error-100: var(--color-red-100);
  --color-error-200: var(--color-red-200);
  --color-error-300: var(--color-red-300);
  --color-error-400: var(--color-red-400);
  --color-error-500: var(--color-red-500);
  --color-error-600: var(--color-red-600);
  --color-error-700: var(--color-red-700);
  --color-error-800: var(--color-red-800);
  --color-error-900: var(--color-red-900);
  --color-error-950: var(--color-red-950);
}
