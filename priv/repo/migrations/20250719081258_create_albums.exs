defmodule Tunez.Repo.Migrations.CreateAlbums do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:albums, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :name, :text, null: false
      add :year_released, :bigint, null: false
      add :cover_image_url, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :artist_id,
          references(:artists,
            column: :id,
            name: "albums_artist_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false
    end

    create index(:albums, [:artist_id])
  end

  def down do
    drop_if_exists index(:albums, [:artist_id])

    drop constraint(:albums, "albums_artist_id_fkey")

    drop table(:albums)
  end
end
