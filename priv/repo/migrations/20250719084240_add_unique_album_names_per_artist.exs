defmodule Tunez.Repo.Migrations.AddUniqueAlbumNamesPerArtist do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create unique_index(:albums, [:name, :artist_id],
             name: "albums_unique_album_names_per_artist_index"
           )
  end

  def down do
    drop_if_exists unique_index(:albums, [:name, :artist_id],
                     name: "albums_unique_album_names_per_artist_index"
                   )
  end
end
