defmodule Tunez.Repo.Migrations.ConfigureReferenceForAlbumArtistId do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:albums, "albums_artist_id_fkey")

    alter table(:albums) do
      modify :artist_id,
             references(:artists,
               column: :id,
               name: "albums_artist_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all
             )
    end
  end

  def down do
    drop constraint(:albums, "albums_artist_id_fkey")

    alter table(:albums) do
      modify :artist_id,
             references(:artists,
               column: :id,
               name: "albums_artist_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end
  end
end
