defmodule Tunez.Repo.Migrations.AddGinIndexForArtistNameSearch do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create index(:artists, ["name gin_trgm_ops"], name: "artists_name_gin_index", using: "GIN")
  end

  def down do
    drop_if_exists index(:artists, ["name gin_trgm_ops"], name: "artists_name_gin_index")
  end
end
