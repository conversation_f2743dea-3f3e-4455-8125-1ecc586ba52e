# Seed data for some sample artists.
# All text content generated by ChatGPT 4.

require Ash.Query
import Ecto.Query

data = Tunez.Seeder.artists()
names = Enum.map(data, & &1.name)

# Delete the existing records for the seed data artists
# This may be run before the `destroy` action is defined on the resource,
# so may need to use Ecto directly.
if Ash.Resource.Info.action(Tunez.Music.Artist, :destroy) do
  Tunez.Music.Artist
  |> Ash.Query.filter(name in ^names)
  |> Ash.bulk_destroy!(:destroy, %{}, strategy: :stream, authorize?: false)
else
  from(a in Tunez.Music.Artist, where: a.name in ^names)
  |> Tunez.Repo.delete_all()
end

# And re-insert fresh copies of them
data
|> Ash.bulk_create!(Tunez.Music.Artist, :create, return_errors?: true, authorize?: false)
