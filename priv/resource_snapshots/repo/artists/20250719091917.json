{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": true, "default": "[]", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "previous_names", "type": ["array", "text"]}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "biography", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [{"all_tenants?": false, "concurrently": false, "error_fields": [], "fields": [{"type": "string", "value": "name gin_trgm_ops"}], "include": null, "message": null, "name": "artists_name_gin_index", "nulls_distinct": true, "prefix": null, "table": null, "unique": false, "using": "GIN", "where": null}], "custom_statements": [], "has_create_action": true, "hash": "F2AD0465C8EA0ED61B0A7D913E160C626EE61902A1C08B9CD514FF5F7EB90852", "identities": [], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Tunez.Repo", "schema": null, "table": "artists"}